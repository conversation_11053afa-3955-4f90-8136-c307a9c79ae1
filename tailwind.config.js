/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./components/**/*.{js,ts,jsx,tsx,mdx}",
		"./app/**/*.{js,ts,jsx,tsx,mdx}",
		"*.{js,ts,jsx,tsx,mdx}",
	],
	theme: {
		extend: {
			colors: {
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",
				primary: {
					DEFAULT: "#C4975C", // Updated to match the new gold
					foreground: "#FFFFFF",
				},
				secondary: {
					DEFAULT: "#817B66",
					foreground: "#FFFFFF",
				},
				destructive: {
					DEFAULT: "hsl(var(--destructive))",
					foreground: "hsl(var(--destructive-foreground))",
				},
				muted: {
					DEFAULT: "#F5EDE2",
					foreground: "#817B66",
				},
				accent: {
					DEFAULT: "#D8C8AE",
					foreground: "#817B66",
				},
				popover: {
					DEFAULT: "hsl(var(--popover))",
					foreground: "hsl(var(--popover-foreground))",
				},
				card: {
					DEFAULT: "hsl(var(--card))",
					foreground: "hsl(var(--card-foreground))",
				},
				// Custom brand colors
				gold: "#C4975C", // Changed from #B58516 to a warmer, more elegant gold
				olive: "#817B66",
				sand: "#D8C8AE",
				cream: "#F5EDE2",
			},
			fontFamily: {
				"new-york": ["Times New Roman", "serif"],
				crimson: ["Crimson Text", "serif"],
				doulos: ["Doulos SIL", "serif"],
				sans: ["Inter", "sans-serif"],
			},
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			animation: {
				"fade-in": "fadeIn 0.8s ease-in-out",
				"slide-up": "slideUp 0.6s ease-out",
				float: "float 3s ease-in-out infinite",
			},
			keyframes: {
				fadeIn: {
					"0%": { opacity: "0", transform: "translateY(20px)" },
					"100%": { opacity: "1", transform: "translateY(0)" },
				},
				slideUp: {
					"0%": { opacity: "0", transform: "translateY(40px)" },
					"100%": { opacity: "1", transform: "translateY(0)" },
				},
				float: {
					"0%, 100%": { transform: "translateY(0px)" },
					"50%": { transform: "translateY(-10px)" },
				},
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
