/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./components/**/*.{js,ts,jsx,tsx,mdx}",
		"./app/**/*.{js,ts,jsx,tsx,mdx}",
		"*.{js,ts,jsx,tsx,mdx}",
	],
	theme: {
		extend: {
			colors: {
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",
				primary: {
					DEFAULT: "#d8af4c", // New Instagram gold
					foreground: "#FFFFFF",
				},
				secondary: {
					DEFAULT: "#36b0d8", // New Instagram blue
					foreground: "#FFFFFF",
				},
				destructive: {
					DEFAULT: "hsl(var(--destructive))",
					foreground: "hsl(var(--destructive-foreground))",
				},
				muted: {
					DEFAULT: "#f8f9fa", // Light neutral background
					foreground: "#4d92d4", // Blue variant for text
				},
				accent: {
					DEFAULT: "#4d92d4", // Blue variant
					foreground: "#FFFFFF",
				},
				popover: {
					DEFAULT: "hsl(var(--popover))",
					foreground: "hsl(var(--popover-foreground))",
				},
				card: {
					DEFAULT: "hsl(var(--card))",
					foreground: "hsl(var(--card-foreground))",
				},
				// New Instagram brand colors
				gold: "#d8af4c", // Primary gold
				"gold-dark": "#b49117", // Dark gold
				blue: "#36b0d8", // Primary blue
				"blue-variant": "#4d92d4", // Blue variant
				// Keep some neutral colors for backgrounds
				cream: "#fefefe", // Almost white
				sand: "#f8f9fa", // Light grey
				olive: "#2c3e50", // Dark text color
			},
			fontFamily: {
				"early-sunday": ["Early Sunday", "serif"],
				"dm-serif": ["DM Serif Display", "serif"],
				"new-york": ["Times New Roman", "serif"],
				crimson: ["Crimson Text", "serif"],
				doulos: ["Doulos SIL", "serif"],
				sans: ["Inter", "sans-serif"],
			},
			fontSize: {
				xs: ["0.875rem", { lineHeight: "1.25rem" }], // Increased from 0.75rem
				sm: ["1rem", { lineHeight: "1.5rem" }], // Increased from 0.875rem
				base: ["1.125rem", { lineHeight: "1.75rem" }], // Increased from 1rem
				lg: ["1.25rem", { lineHeight: "1.875rem" }], // Increased from 1.125rem
				xl: ["1.5rem", { lineHeight: "2rem" }], // Increased from 1.25rem
				"2xl": ["1.875rem", { lineHeight: "2.25rem" }], // Increased from 1.5rem
				"3xl": ["2.25rem", { lineHeight: "2.75rem" }], // Increased from 1.875rem
				"4xl": ["2.75rem", { lineHeight: "3rem" }], // Increased from 2.25rem
				"5xl": ["3.5rem", { lineHeight: "1.1" }], // Increased from 3rem
				"6xl": ["4.25rem", { lineHeight: "1.1" }], // Increased from 3.75rem
				"7xl": ["5rem", { lineHeight: "1.1" }], // Increased from 4.5rem
				"8xl": ["6.5rem", { lineHeight: "1.1" }], // Increased from 6rem
				"9xl": ["8.5rem", { lineHeight: "1.1" }], // Increased from 8rem
			},
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			animation: {
				"fade-in": "fadeIn 0.8s ease-in-out",
				"slide-up": "slideUp 0.6s ease-out",
				float: "float 3s ease-in-out infinite",
			},
			keyframes: {
				fadeIn: {
					"0%": { opacity: "0", transform: "translateY(20px)" },
					"100%": { opacity: "1", transform: "translateY(0)" },
				},
				slideUp: {
					"0%": { opacity: "0", transform: "translateY(40px)" },
					"100%": { opacity: "1", transform: "translateY(0)" },
				},
				float: {
					"0%, 100%": { transform: "translateY(0px)" },
					"50%": { transform: "translateY(-10px)" },
				},
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
