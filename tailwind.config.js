/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./components/**/*.{js,ts,jsx,tsx,mdx}",
		"./app/**/*.{js,ts,jsx,tsx,mdx}",
		"*.{js,ts,jsx,tsx,mdx}",
	],
	theme: {
		extend: {
			colors: {
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",
				primary: {
					DEFAULT: "#d8af4c", // New Instagram gold
					foreground: "#FFFFFF",
				},
				secondary: {
					DEFAULT: "#36b0d8", // New Instagram blue
					foreground: "#FFFFFF",
				},
				destructive: {
					DEFAULT: "hsl(var(--destructive))",
					foreground: "hsl(var(--destructive-foreground))",
				},
				muted: {
					DEFAULT: "#f8f9fa", // Light neutral background
					foreground: "#4d92d4", // Blue variant for text
				},
				accent: {
					DEFAULT: "#4d92d4", // Blue variant
					foreground: "#FFFFFF",
				},
				popover: {
					DEFAULT: "hsl(var(--popover))",
					foreground: "hsl(var(--popover-foreground))",
				},
				card: {
					DEFAULT: "hsl(var(--card))",
					foreground: "hsl(var(--card-foreground))",
				},
				// New Instagram brand colors
				gold: "#d8af4c", // Primary gold
				"gold-dark": "#b49117", // Dark gold
				blue: "#36b0d8", // Primary blue
				"blue-variant": "#4d92d4", // Blue variant
				// Keep some neutral colors for backgrounds
				cream: "#fefefe", // Almost white
				sand: "#f8f9fa", // Light grey
				olive: "#2c3e50", // Dark text color
			},
			fontFamily: {
				"early-sunday": ["Early Sunday", "serif"],
				"dm-serif": ["DM Serif Display", "serif"],
				"new-york": ["Times New Roman", "serif"],
				crimson: ["Crimson Text", "serif"],
				doulos: ["Doulos SIL", "serif"],
				sans: ["Inter", "sans-serif"],
			},
			fontSize: {
				xs: ["0.75rem", { lineHeight: "1rem" }],
				sm: ["0.875rem", { lineHeight: "1.25rem" }],
				base: ["1rem", { lineHeight: "1.5rem" }],
				lg: ["1.125rem", { lineHeight: "1.75rem" }],
				xl: ["1.25rem", { lineHeight: "1.75rem" }],
				"2xl": ["1.5rem", { lineHeight: "2rem" }],
				"3xl": ["1.875rem", { lineHeight: "2.25rem" }],
				"4xl": ["2.25rem", { lineHeight: "2.5rem" }],
				"5xl": ["3rem", { lineHeight: "1" }],
				"6xl": ["3.75rem", { lineHeight: "1" }],
				"7xl": ["4.5rem", { lineHeight: "1" }],
				"8xl": ["6rem", { lineHeight: "1" }],
				"9xl": ["8rem", { lineHeight: "1" }],
			},
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			animation: {
				"fade-in": "fadeIn 0.8s ease-in-out",
				"slide-up": "slideUp 0.6s ease-out",
				float: "float 3s ease-in-out infinite",
			},
			keyframes: {
				fadeIn: {
					"0%": { opacity: "0", transform: "translateY(20px)" },
					"100%": { opacity: "1", transform: "translateY(0)" },
				},
				slideUp: {
					"0%": { opacity: "0", transform: "translateY(40px)" },
					"100%": { opacity: "1", transform: "translateY(0)" },
				},
				float: {
					"0%, 100%": { transform: "translateY(0px)" },
					"50%": { transform: "translateY(-10px)" },
				},
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
