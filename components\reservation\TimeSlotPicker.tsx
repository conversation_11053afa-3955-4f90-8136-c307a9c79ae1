"use client"

import { useState, useEffect } from "react"
import { Clock } from "lucide-react"

interface TimeSlotPickerProps {
  selectedDate: string
  selectedTime: string
  onTimeSelect: (time: string) => void
}

export default function TimeSlotPicker({ selectedDate, selectedTime, onTimeSelect }: TimeSlotPickerProps) {
  const [availableSlots, setAvailableSlots] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!selectedDate) return

    setLoading(true)

    // TODO: connect backend here
    // Simulate API call to get available time slots for the selected date
    setTimeout(() => {
      // Generate mock time slots
      const allSlots = [
        "09:00",
        "09:30",
        "10:00",
        "10:30",
        "11:00",
        "11:30",
        "14:00",
        "14:30",
        "15:00",
        "15:30",
        "16:00",
        "16:30",
        "17:00",
        "17:30",
        "18:00",
        "18:30",
      ]

      // Randomly remove some slots to simulate unavailability
      const available = allSlots.filter(() => Math.random() > 0.4)

      setAvailableSlots(available)
      setLoading(false)
    }, 800)
  }, [selectedDate])

  if (loading) {
    return (
      <div className="bg-white/50 rounded-lg p-6 border border-sand/30">
        <div className="flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-gold/30 border-t-gold rounded-full animate-spin mr-3"></div>
          <span className="font-doulos text-olive/70">Chargement des créneaux...</span>
        </div>
      </div>
    )
  }

  if (availableSlots.length === 0) {
    return (
      <div className="bg-white/50 rounded-lg p-6 border border-sand/30 text-center">
        <Clock className="w-8 h-8 text-olive/40 mx-auto mb-3" />
        <p className="font-doulos text-olive/70">Aucun créneau disponible pour cette date.</p>
        <p className="font-doulos text-olive/60 text-sm mt-1">Veuillez choisir une autre date.</p>
      </div>
    )
  }

  const formatTime = (time: string) => {
    return time.replace(":", "h")
  }

  const getTimeOfDay = (time: string) => {
    const hour = Number.parseInt(time.split(":")[0])
    if (hour < 12) return "Matin"
    if (hour < 17) return "Après-midi"
    return "Fin de journée"
  }

  // Group slots by time of day
  const groupedSlots = availableSlots.reduce((groups: { [key: string]: string[] }, slot) => {
    const timeOfDay = getTimeOfDay(slot)
    if (!groups[timeOfDay]) {
      groups[timeOfDay] = []
    }
    groups[timeOfDay].push(slot)
    return groups
  }, {})

  return (
    <div className="bg-white/50 rounded-lg p-4 border border-sand/30">
      <div className="space-y-4">
        {Object.entries(groupedSlots).map(([timeOfDay, slots]) => (
          <div key={timeOfDay}>
            <h4 className="font-doulos font-medium text-olive mb-3 text-sm">{timeOfDay}</h4>
            <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
              {slots.map((slot) => (
                <button
                  key={slot}
                  type="button"
                  onClick={() => onTimeSelect(slot)}
                  className={`
                    py-2 px-3 rounded-lg text-sm font-doulos transition-all duration-200
                    ${
                      selectedTime === slot
                        ? "bg-gold text-white shadow-md"
                        : "bg-white/80 text-olive hover:bg-gold/10 hover:text-gold border border-sand/30"
                    }
                  `}
                >
                  {formatTime(slot)}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 text-center">
        <p className="font-doulos text-olive/60 text-xs">Sélectionnez l'heure qui vous convient le mieux</p>
      </div>
    </div>
  )
}
