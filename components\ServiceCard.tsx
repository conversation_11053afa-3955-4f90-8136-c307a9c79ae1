import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Header } from "@/components/ui/card";
import { ArrowRight, Clock, Euro } from "lucide-react";
import Link from "next/link";
import type React from "react";

interface ServiceCardProps {
	title: string;
	description: string;
	duration: string;
	price: string;
	features: string[];
	href: string;
	icon: React.ReactNode;
}

const ServiceCard = ({ title, description, duration, price, features, href, icon }: ServiceCardProps) => {
	return (
		<Card className="group bg-white border-sand hover:border-gold transition-all duration-300 hover:shadow-xl hover:-translate-y-2 h-full">
			<CardHeader className="text-center pb-4">
				<div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
					<div className="text-gold">{icon}</div>
				</div>
				<h3 className="font-new-york text-xl font-bold text-olive mb-2">{title}</h3>
				<p className="font-doulos text-olive/70 text-sm leading-relaxed">{description}</p>
			</CardHeader>

			<CardContent className="space-y-4">
				{/* Duration & Price */}
				<div className="flex justify-between items-center py-3 px-4 bg-cream rounded-lg">
					<div className="flex items-center text-olive/80">
						<Clock className="w-4 h-4 mr-2" />
						<span className="font-doulos text-sm">{duration}</span>
					</div>
					<div className="flex items-center text-gold font-semibold">
						<Euro className="w-4 h-4 mr-1" />
						<span className="font-doulos">{price}</span>
					</div>
				</div>

				{/* Features */}
				<ul className="space-y-2">
					{features.map((feature, index) => (
						<li key={index} className="flex items-start text-sm text-olive/80">
							<div className="w-1.5 h-1.5 bg-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
							<span className="font-doulos">{feature}</span>
						</li>
					))}
				</ul>
			</CardContent>

			<CardFooter className="pt-4">
				<Button
					asChild
					className="w-full bg-gold hover:bg-gold/90 text-white font-doulos font-medium rounded-full transition-all duration-300 group-hover:shadow-lg min-h-[48px]"
				>
					<Link
						href={`/reservation/${href.split("/").pop()}`}
						className="flex items-center justify-center py-3 px-6"
					>
						En savoir plus
						<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
					</Link>
				</Button>
			</CardFooter>
		</Card>
	);
};

export default ServiceCard;
