import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from "@/components/ui/card";
import { ArrowRight, Clock, Euro } from "lucide-react";
import Link from "next/link";
import type React from "react";

interface ServiceCardProps {
	title: string;
	description: string;
	duration: string;
	price: string;
	features: string[];
	href: string;
	icon: React.ReactNode;
}

const ServiceCard = ({ title, description, duration, price, features, href, icon }: ServiceCardProps) => {
	return (
		<Card className="group bg-white border-2 border-sand hover:border-gold transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 h-full">
			<CardHeader className="text-center pb-6">
				<div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-gold/20 to-gold/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
					<div className="text-gold">{icon}</div>
				</div>
				<div className="mb-4">
					<h3 className="font-dm-serif text-2xl font-bold text-olive mb-3">{title}</h3>
					{/* Price prominently displayed right after title */}
					<div className="flex items-center justify-center mb-4">
						<div className="bg-gold/15 px-4 py-2 rounded-full border border-gold/30">
							<div className="flex items-center text-gold font-bold text-lg">
								<Euro className="w-5 h-5 mr-1" />
								<span className="font-sans">{price}€</span>
							</div>
						</div>
						<div className="ml-4 flex items-center text-olive/70">
							<Clock className="w-4 h-4 mr-2" />
							<span className="font-sans text-sm font-medium">{duration}</span>
						</div>
					</div>
				</div>
				<p className="font-sans text-olive/80 text-base leading-relaxed font-medium">{description}</p>
			</CardHeader>

			<CardContent className="space-y-6 px-6">
				{/* Features */}
				<ul className="space-y-3">
					{features.map((feature, index) => (
						<li key={index} className="flex items-start text-base text-olive/80">
							<div className="w-2 h-2 bg-gold rounded-full mt-2 mr-4 flex-shrink-0"></div>
							<span className="font-sans font-medium">{feature}</span>
						</li>
					))}
				</ul>
			</CardContent>

			<CardFooter className="pt-6 px-6">
				<Button
					asChild
					className="w-full bg-gold hover:bg-gold-dark text-white font-sans font-bold rounded-full transition-all duration-300 group-hover:shadow-xl min-h-[56px] text-lg"
				>
					<Link
						href={`/reservation/${href.split("/").pop()}`}
						className="flex items-center justify-center py-4 px-6"
					>
						Réserver maintenant
						<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
					</Link>
				</Button>
			</CardFooter>
		</Card>
	);
};

export default ServiceCard;
