"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Download, Mail, Calendar, Home, ArrowRight } from "lucide-react"

export default function PaiementSuccesPage() {
  const [transactionData, setTransactionData] = useState<any>(null)

  useEffect(() => {
    // TODO: connect backend here
    // Get transaction data from backend
    const mockData = {
      transactionId: "TXN-" + Math.random().toString(36).substr(2, 9).toUpperCase(),
      service: "Soin énergétique complet",
      amount: 85,
      date: "2024-03-15",
      time: "14:00",
      customerName: "<PERSON>",
      email: "<EMAIL>",
      paymentMethod: "Carte bancaire ****3456",
      confirmationNumber: "RDV-" + Math.random().toString(36).substr(2, 9).toUpperCase(),
    }
    setTransactionData(mockData)
  }, [])

  if (!transactionData) {
    return (
      <div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-gold/30 border-t-gold rounded-full animate-spin mx-auto mb-4"></div>
          <p className="font-doulos text-olive/70">Chargement...</p>
        </div>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Success Header */}
        <div className="text-center mb-12">
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-16 h-16 text-green-600" />
          </div>
          <h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-4">
            Paiement validé avec succès !
          </h1>
          <p className="font-doulos text-lg text-olive/80 max-w-2xl mx-auto">
            Merci pour votre confiance. Votre réservation est confirmée et votre paiement a été traité avec succès.
          </p>
        </div>

        {/* Transaction Summary */}
        <Card className="bg-white/80 backdrop-blur-sm border-sand/30 shadow-xl mb-8">
          <CardHeader>
            <CardTitle className="font-new-york text-2xl text-olive text-center">
              Récapitulatif de la transaction
            </CardTitle>
            <div className="text-center">
              <span className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium">
                Transaction ID : {transactionData.transactionId}
              </span>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Payment Details */}
            <div className="bg-gradient-to-br from-gold/5 to-cream/30 rounded-lg p-6">
              <h3 className="font-new-york text-lg font-semibold text-olive mb-4">Détails du paiement</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <p className="font-doulos text-olive/70 text-sm">Service</p>
                  <p className="font-doulos text-olive font-medium">{transactionData.service}</p>
                </div>
                <div>
                  <p className="font-doulos text-olive/70 text-sm">Montant payé</p>
                  <p className="font-new-york text-2xl font-bold text-gold">{transactionData.amount}€</p>
                </div>
                <div>
                  <p className="font-doulos text-olive/70 text-sm">Méthode de paiement</p>
                  <p className="font-doulos text-olive font-medium">{transactionData.paymentMethod}</p>
                </div>
                <div>
                  <p className="font-doulos text-olive/70 text-sm">Date de transaction</p>
                  <p className="font-doulos text-olive font-medium">
                    {new Date().toLocaleDateString("fr-FR")} à {new Date().toLocaleTimeString("fr-FR")}
                  </p>
                </div>
              </div>
            </div>

            {/* Appointment Details */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <Calendar className="w-5 h-5 text-gold mt-1" />
                <div>
                  <h4 className="font-new-york font-semibold text-olive">Rendez-vous confirmé</h4>
                  <p className="font-doulos text-olive/80 capitalize">{formatDate(transactionData.date)}</p>
                  <p className="font-doulos text-olive/80">à {transactionData.time}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-gold mt-1" />
                <div>
                  <h4 className="font-new-york font-semibold text-olive">Confirmation envoyée</h4>
                  <p className="font-doulos text-olive/80">{transactionData.email}</p>
                  <p className="font-doulos text-olive/60 text-sm">N° : {transactionData.confirmationNumber}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-gold/5 to-cream/30 border-sand/30">
            <CardContent className="p-6 text-center">
              <Download className="w-8 h-8 text-gold mx-auto mb-3" />
              <h3 className="font-new-york text-lg font-semibold text-olive mb-2">Télécharger la facture</h3>
              <p className="font-doulos text-olive/80 text-sm mb-4">Recevez votre facture au format PDF</p>
              <Button
                variant="outline"
                className="border-gold text-gold hover:bg-gold hover:text-white font-doulos rounded-full"
                onClick={() => {
                  // TODO: connect backend here
                  alert("Téléchargement de la facture...")
                }}
              >
                Télécharger PDF
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-olive/5 to-sand/20 border-sand/30">
            <CardContent className="p-6 text-center">
              <Calendar className="w-8 h-8 text-gold mx-auto mb-3" />
              <h3 className="font-new-york text-lg font-semibold text-olive mb-2">Ajouter au calendrier</h3>
              <p className="font-doulos text-olive/80 text-sm mb-4">N'oubliez pas votre rendez-vous</p>
              <Button
                variant="outline"
                className="border-gold text-gold hover:bg-gold hover:text-white font-doulos rounded-full"
                onClick={() => {
                  // TODO: connect backend here
                  alert("Ajout au calendrier...")
                }}
              >
                Ajouter l'événement
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Next Steps */}
        <Card className="bg-white/80 backdrop-blur-sm border-sand/30 mb-8">
          <CardContent className="p-6">
            <h3 className="font-new-york text-lg font-semibold text-olive mb-4 text-center">Prochaines étapes</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-gold text-sm font-bold">1</span>
                </div>
                <div>
                  <h4 className="font-doulos font-medium text-olive">Confirmation par email</h4>
                  <p className="font-doulos text-olive/80 text-sm">
                    Vous recevrez un email de confirmation avec tous les détails dans les prochaines minutes
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-gold text-sm font-bold">2</span>
                </div>
                <div>
                  <h4 className="font-doulos font-medium text-olive">Rappel avant le rendez-vous</h4>
                  <p className="font-doulos text-olive/80 text-sm">
                    Je vous contacterai 24h avant pour confirmer et vous donner les dernières informations
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-gold text-sm font-bold">3</span>
                </div>
                <div>
                  <h4 className="font-doulos font-medium text-olive">Préparation de la séance</h4>
                  <p className="font-doulos text-olive/80 text-sm">
                    Préparez vos questions et venez avec une intention claire pour optimiser notre temps ensemble
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button
            asChild
            size="lg"
            className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
          >
            <Link href="/" className="flex items-center">
              <Home className="mr-2 w-4 h-4" />
              Retour à l'accueil
            </Link>
          </Button>

          <Button
            asChild
            variant="outline"
            size="lg"
            className="border-2 border-gold text-gold hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
          >
            <Link href="/reservation" className="flex items-center">
              Réserver un autre service
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </Button>
        </div>

        {/* Support */}
        <div className="mt-12 text-center">
          <p className="font-doulos text-olive/60 text-sm mb-2">
            Une question sur votre réservation ou votre paiement ?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center text-sm">
            <a href="tel:0612345678" className="flex items-center text-gold hover:text-gold/80 transition-colors">
              <span>📞 06 12 34 56 78</span>
            </a>
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-gold hover:text-gold/80 transition-colors"
            >
              <span>✉️ <EMAIL></span>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
