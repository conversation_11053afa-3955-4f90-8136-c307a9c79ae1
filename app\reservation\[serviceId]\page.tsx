"use client";

import ProgressBar from "@/components/reservation/ProgressBar";
import ReservationForm from "@/components/reservation/ReservationForm";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Clock, Compass, Euro, Heart, Shield, Sparkles, Zap } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

// Mock data for services (same as reservation page)
const services = [
	{
		id: "soin-energetique",
		name: "Soin énergétique complet",
		description:
			"Rééquilibrage global de vos énergies pour retrouver harmonie et vitalité dans tous les aspects de votre vie",
		duration: "1h30",
		price: 85,
		icon: <Sparkles className="w-8 h-8" />,
		type: "soin",
		details: [
			"Analyse énergétique approfondie",
			"Nettoyage et harmonisation des chakras",
			"Libération des blocages énergétiques",
			"Conseils personnalisés post-séance",
			"Suivi et accompagnement",
		],
	},
	{
		id: "coaching-bien-etre",
		name: "Coaching bien-être",
		description: "Accompagnement personnalisé pour développer votre potentiel et créer la vie qui vous ressemble",
		duration: "1h",
		price: 65,
		icon: <Heart className="w-8 h-8" />,
		type: "coaching",
		details: [
			"Définition d'objectifs clairs et réalisables",
			"Techniques de développement personnel",
			"Plan d'action personnalisé",
			"Outils pratiques au quotidien",
			"Suivi régulier et ajustements",
		],
	},
	{
		id: "harmonisation-chakras",
		name: "Harmonisation des chakras",
		description: "Équilibrage de vos centres énergétiques pour une circulation optimale de l'énergie vitale",
		duration: "1h",
		price: 70,
		icon: <Zap className="w-8 h-8" />,
		type: "soin",
		details: [
			"Diagnostic des 7 chakras principaux",
			"Techniques de rééquilibrage spécifiques",
			"Méditation guidée personnalisée",
			"Conseils d'entretien énergétique",
			"Support audio pour la pratique",
		],
	},
	{
		id: "liberation-emotionnelle",
		name: "Libération émotionnelle",
		description: "Techniques douces pour libérer les émotions bloquées et retrouver votre liberté intérieure",
		duration: "1h15",
		price: 75,
		icon: <Shield className="w-8 h-8" />,
		type: "soin",
		details: [
			"Identification des blocages émotionnels",
			"Techniques EFT et libération énergétique",
			"Travail sur les mémoires cellulaires",
			"Intégration et ancrage des changements",
			"Exercices de maintien à domicile",
		],
	},
	{
		id: "soin-distance",
		name: "Soin à distance",
		description: "Séance énergétique efficace depuis chez vous, avec la même qualité qu'en présentiel",
		duration: "1h",
		price: 60,
		icon: <Compass className="w-8 h-8" />,
		type: "soin",
		details: [
			"Connexion énergétique à distance",
			"Soin complet par téléphone/visio",
			"Feedback détaillé en temps réel",
			"Enregistrement audio du soin",
			"Suivi personnalisé par email",
		],
	},
	{
		id: "accompagnement-grossesse",
		name: "Accompagnement grossesse",
		description:
			"Soutien énergétique spécialisé pour vivre sereinement votre grossesse et préparer l'arrivée de bébé",
		duration: "1h",
		price: 70,
		icon: <Heart className="w-8 h-8" />,
		type: "soin",
		details: [
			"Harmonisation mère-enfant",
			"Gestion du stress et des peurs",
			"Préparation énergétique à l'accouchement",
			"Connexion avec votre bébé",
			"Accompagnement post-natal",
		],
	},
];

export default function ServiceReservationPage() {
	const params = useParams();
	const router = useRouter();
	const serviceId = params.serviceId as string;
	const [service, setService] = useState<any>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		// TODO: connect backend here
		// Simulate API call
		setTimeout(() => {
			const foundService = services.find((s) => s.id === serviceId);
			if (foundService) {
				// Check if it's a coaching service - redirect to first encounter
				if (foundService.type === "coaching") {
					router.push("/reservation/first-encounter");
					return;
				}
				setService(foundService);
			}
			setLoading(false);
		}, 500);
	}, [serviceId, router]);

	if (loading) {
		return (
			<div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30 flex items-center justify-center">
				<div className="text-center">
					<div className="w-12 h-12 border-4 border-gold/30 border-t-gold rounded-full animate-spin mx-auto mb-4"></div>
					<p className="font-doulos text-olive/70">Chargement...</p>
				</div>
			</div>
		);
	}

	if (!service) {
		return (
			<div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30 flex items-center justify-center">
				<div className="text-center">
					<h1 className="font-new-york text-2xl font-bold text-olive mb-4">Service non trouvé</h1>
					<Button asChild variant="outline" className="border-gold text-gold hover:bg-gold hover:text-white">
						<Link href="/reservation">Retour aux services</Link>
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30">
			{/* Progress Bar */}
			<ProgressBar
				currentStep={2}
				totalSteps={4}
				steps={["Choix du service", "Informations", "Confirmation", "Paiement"]}
			/>

			<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
				{/* Back Button */}
				<div className="mb-8">
					<Button asChild variant="ghost" className="text-olive hover:text-gold">
						<Link href="/reservation" className="flex items-center">
							<ArrowLeft className="w-4 h-4 mr-2" />
							Retour au choix des services
						</Link>
					</Button>
				</div>

				{/* Step Indicator */}
				<div className="text-center mb-8">
					<div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20">
						<span className="text-sm font-crimson text-gold font-medium">Étape 2/4 - Vos informations</span>
					</div>
				</div>

				<div className="grid lg:grid-cols-2 gap-12">
					{/* Service Details */}
					<div>
						<Card className="bg-white/80 backdrop-blur-sm border-sand/30 mb-8">
							<CardHeader>
								<div className="flex items-center space-x-4 mb-4">
									<div className="w-16 h-16 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center">
										<div className="text-gold">{service.icon}</div>
									</div>
									<div className="flex-1">
										<CardTitle className="font-new-york text-2xl text-olive mb-2">
											{service.name}
										</CardTitle>
										<div className="flex items-center space-x-4 text-sm text-olive/70">
											<div className="flex items-center">
												<Clock className="w-4 h-4 mr-1" />
												<span className="font-doulos">{service.duration}</span>
											</div>
											<div className="flex items-center text-gold font-semibold">
												<Euro className="w-4 h-4 mr-1" />
												<span className="font-doulos">{service.price}€</span>
											</div>
										</div>
									</div>
								</div>
								<p className="font-doulos text-olive/80 leading-relaxed">{service.description}</p>
							</CardHeader>

							<CardContent>
								<h4 className="font-new-york text-lg font-semibold text-olive mb-4">
									Ce qui est inclus :
								</h4>
								<ul className="space-y-2">
									{service.details.map((detail: string, index: number) => (
										<li key={index} className="flex items-start">
											<div className="w-1.5 h-1.5 bg-gold rounded-full mt-2 mr-3 flex-shrink-0"></div>
											<span className="font-doulos text-olive/80 text-sm">{detail}</span>
										</li>
									))}
								</ul>
							</CardContent>
						</Card>

						{/* Additional Info */}
						<Card className="bg-gradient-to-br from-gold/5 to-cream/50 border-sand/30">
							<CardContent className="p-6">
								<h4 className="font-new-york text-lg font-semibold text-olive mb-3">
									Informations pratiques
								</h4>
								<div className="space-y-2 text-sm font-doulos text-olive/80">
									<p>• Séance disponible en cabinet ou à distance</p>
									<p>• Annulation possible jusqu'à 24h avant</p>
									<p>• Suivi personnalisé inclus</p>
									<p>• Paiement sécurisé</p>
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Reservation Form */}
					<div>
						<ReservationForm service={service} />
					</div>
				</div>
			</div>
		</div>
	);
}
