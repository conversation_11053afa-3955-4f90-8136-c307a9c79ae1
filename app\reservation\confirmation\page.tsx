"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Calendar, Clock, MapPin, Phone, Mail, ArrowRight, Home } from "lucide-react"
import ProgressBar from "@/components/reservation/ProgressBar"

export default function ConfirmationPage() {
  const searchParams = useSearchParams()
  const [reservationData, setReservationData] = useState<any>(null)

  useEffect(() => {
    // TODO: connect backend here
    // Get reservation data from URL params or localStorage
    const mockData = {
      service: searchParams.get("service") || "Soin énergétique complet",
      firstName: searchParams.get("firstName") || "Marie",
      lastName: searchParams.get("lastName") || "Dupont",
      email: searchParams.get("email") || "<EMAIL>",
      phone: searchParams.get("phone") || "06 12 34 56 78",
      date: searchParams.get("date") || "2024-03-15",
      time: searchParams.get("time") || "14:00",
      duration: searchParams.get("duration") || "1h30",
      price: searchParams.get("price") || "85",
      type: searchParams.get("type") || "cabinet",
      confirmationNumber: "RDV-" + Math.random().toString(36).substr(2, 9).toUpperCase(),
    }
    setReservationData(mockData)
  }, [searchParams])

  if (!reservationData) {
    return (
      <div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-gold/30 border-t-gold rounded-full animate-spin mx-auto mb-4"></div>
          <p className="font-doulos text-olive/70">Chargement...</p>
        </div>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30">
      {/* Progress Bar - Different steps based on service type */}
      <ProgressBar
        currentStep={3}
        totalSteps={reservationData?.service === "Entretien découverte" ? 3 : 4}
        steps={
          reservationData?.service === "Entretien découverte"
            ? ["Entretien découverte", "Informations", "Confirmation"]
            : ["Choix du service", "Informations", "Confirmation", "Paiement"]
        }
      />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Success Header */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 border border-green-200 mb-4">
            <span className="text-sm font-crimson text-green-700 font-medium">
              {reservationData?.service === "Entretien découverte" ? "Étape 3/3 - Confirmé" : "Étape 3/4 - Confirmé"}
            </span>
          </div>
          <h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-4">Rendez-vous confirmé !</h1>
          <p className="font-doulos text-lg text-olive/80 max-w-2xl mx-auto">
            Merci {reservationData?.firstName}, votre rendez-vous a bien été enregistré. Vous allez recevoir un email de
            confirmation avec tous les détails.
          </p>
        </div>

        {/* Reservation Summary */}
        <Card className="bg-white/80 backdrop-blur-sm border-sand/30 shadow-xl mb-8">
          <CardHeader>
            <CardTitle className="font-new-york text-2xl text-olive text-center">
              Récapitulatif de votre rendez-vous
            </CardTitle>
            <div className="text-center">
              <span className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
                N° de confirmation : {reservationData?.confirmationNumber}
              </span>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Service Info */}
            <div className="bg-gradient-to-br from-gold/5 to-cream/30 rounded-lg p-6">
              <h3 className="font-new-york text-lg font-semibold text-olive mb-4">Service réservé</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <p className="font-doulos text-olive font-medium">{reservationData?.service}</p>
                  <div className="flex items-center text-olive/70 mt-2">
                    <Clock className="w-4 h-4 mr-2" />
                    <span className="font-doulos text-sm">Durée : {reservationData?.duration}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-new-york text-2xl font-bold text-gold">
                    {reservationData?.price === "0" ? "Gratuit" : `${reservationData?.price}€`}
                  </p>
                </div>
              </div>
            </div>

            {/* Date & Time */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <Calendar className="w-5 h-5 text-gold mt-1" />
                <div>
                  <h4 className="font-new-york font-semibold text-olive">Date</h4>
                  <p className="font-doulos text-olive/80 capitalize">{formatDate(reservationData?.date)}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-gold mt-1" />
                <div>
                  <h4 className="font-new-york font-semibold text-olive">Heure</h4>
                  <p className="font-doulos text-olive/80">{reservationData?.time}</p>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-gold mt-1" />
                <div>
                  <h4 className="font-new-york font-semibold text-olive">Email</h4>
                  <p className="font-doulos text-olive/80">{reservationData?.email}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-gold mt-1" />
                <div>
                  <h4 className="font-new-york font-semibold text-olive">Téléphone</h4>
                  <p className="font-doulos text-olive/80">{reservationData?.phone}</p>
                </div>
              </div>
            </div>

            {/* Location */}
            <div className="flex items-start space-x-3">
              <MapPin className="w-5 h-5 text-gold mt-1" />
              <div>
                <h4 className="font-new-york font-semibold text-olive">Modalité</h4>
                <p className="font-doulos text-olive/80">
                  {reservationData?.type === "cabinet"
                    ? "En cabinet"
                    : reservationData?.type === "phone"
                      ? "Par téléphone"
                      : "À distance"}
                </p>
                {reservationData?.type === "cabinet" && (
                  <p className="font-doulos text-olive/60 text-sm mt-1">
                    L'adresse exacte vous sera communiquée par email
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="bg-gradient-to-br from-gold/5 to-cream/30 border-sand/30 mb-8">
          <CardContent className="p-6">
            <h3 className="font-new-york text-lg font-semibold text-olive mb-4">Prochaines étapes</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-gold text-xs font-bold">1</span>
                </div>
                <p className="font-doulos text-olive/80 text-sm">
                  Vous allez recevoir un email de confirmation dans les prochaines minutes
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-gold text-xs font-bold">2</span>
                </div>
                <p className="font-doulos text-olive/80 text-sm">
                  Je vous contacterai 24h avant le rendez-vous pour confirmer
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gold/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-gold text-xs font-bold">3</span>
                </div>
                <p className="font-doulos text-olive/80 text-sm">
                  {reservationData?.service === "Entretien découverte"
                    ? "Préparez vos questions pour notre échange téléphonique"
                    : "Préparez vos questions et venez avec une intention claire"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {/* Show payment button only for paid services */}
          {reservationData?.price !== "0" && (
            <Button
              asChild
              size="lg"
              className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
            >
              <Link
                href={`/paiement?service=${encodeURIComponent(reservationData?.service || "")}&price=${reservationData?.price}&date=${reservationData?.date}&time=${reservationData?.time}`}
                className="flex items-center"
              >
                Procéder au paiement
                <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>
          )}

          <Button
            asChild
            size="lg"
            variant={reservationData?.price === "0" ? "default" : "outline"}
            className={`font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg ${
              reservationData?.price === "0"
                ? "bg-gold hover:bg-gold/90 text-white hover:shadow-xl hover:scale-105"
                : "border-2 border-gold text-gold hover:bg-gold hover:text-white"
            }`}
          >
            <Link href="/" className="flex items-center">
              <Home className="mr-2 w-4 h-4" />
              Retour à l'accueil
            </Link>
          </Button>

          <Button
            asChild
            variant="outline"
            size="lg"
            className="border-2 border-gold text-gold hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
          >
            <Link href="/reservation" className="flex items-center">
              Réserver un autre service
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </Button>
        </div>

        {/* Contact Info */}
        <div className="mt-12 text-center">
          <p className="font-doulos text-olive/60 text-sm mb-2">
            Une question ? Besoin de modifier votre rendez-vous ?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center text-sm">
            <a href="tel:0612345678" className="flex items-center text-gold hover:text-gold/80 transition-colors">
              <Phone className="w-4 h-4 mr-2" />
              06 12 34 56 78
            </a>
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-gold hover:text-gold/80 transition-colors"
            >
              <Mail className="w-4 h-4 mr-2" />
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
