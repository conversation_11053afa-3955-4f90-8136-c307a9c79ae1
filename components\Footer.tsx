import Link from "next/link"
import { Heart, Star, Instagram, Facebook, Mail } from "lucide-react"

const Footer = () => {
  return (
    <footer className="bg-olive text-cream">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 rounded-full bg-gold flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-new-york text-xl font-bold text-gold"><PERSON></h3>
                <p className="text-sm font-crimson italic text-cream/80">Coach en soins énergétiques</p>
              </div>
            </div>
            <p className="font-doulos text-cream/80 mb-6 max-w-md leading-relaxed">
              Accompagnement personnalisé pour retrouver votre équilibre intérieur et révéler votre potentiel
              authentique à travers les soins énergétiques et le coaching bien-être.
            </p>
            <div className="flex space-x-4">
              <Link
                href="#"
                className="w-10 h-10 bg-cream/10 hover:bg-gold rounded-full flex items-center justify-center transition-colors duration-300"
              >
                <Instagram className="w-5 h-5" />
              </Link>
              <Link
                href="#"
                className="w-10 h-10 bg-cream/10 hover:bg-gold rounded-full flex items-center justify-center transition-colors duration-300"
              >
                <Facebook className="w-5 h-5" />
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="w-10 h-10 bg-cream/10 hover:bg-gold rounded-full flex items-center justify-center transition-colors duration-300"
              >
                <Mail className="w-5 h-5" />
              </Link>
            </div>
          </div>

          {/* Navigation */}
          <div>
            <h4 className="font-new-york text-lg font-semibold text-gold mb-4">Navigation</h4>
            <ul className="space-y-2">
              {[
                { name: "Accueil", href: "/" },
                { name: "À propos", href: "/about" },
                { name: "Services", href: "/services" },
                { name: "Témoignages", href: "/testimonials" },
                { name: "Événements", href: "/events" },
                { name: "Contact", href: "/contact" },
              ].map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="font-doulos text-cream/80 hover:text-gold transition-colors duration-200"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-new-york text-lg font-semibold text-gold mb-4">Services</h4>
            <ul className="space-y-2">
              {[
                "Soins énergétiques",
                "Coaching bien-être",
                "Harmonisation chakras",
                "Libération émotionnelle",
                "Soins à distance",
              ].map((service) => (
                <li key={service}>
                  <span className="font-doulos text-cream/80 text-sm">{service}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-cream/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-2 text-cream/60 text-sm font-doulos mb-4 md:mb-0">
            <span>© {new Date().getFullYear()} Laurence Gémin</span>
            <span>•</span>
            <span>Fait avec</span>
            <Heart className="w-4 h-4 text-gold fill-current" />
            <span>pour votre bien-être</span>
          </div>

          <div className="flex space-x-6 text-cream/60 text-sm font-doulos">
            <Link href="/mentions-legales" className="hover:text-gold transition-colors duration-200">
              Mentions légales
            </Link>
            <Link href="/politique-confidentialite" className="hover:text-gold transition-colors duration-200">
              Confidentialité
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
