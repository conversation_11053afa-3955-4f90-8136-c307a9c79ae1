"use client"

import FirstEncounterForm from "@/components/reservation/FirstEncounterForm"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Phone, Heart, ArrowLeft, Clock, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import ProgressBar from "@/components/reservation/ProgressBar"

export default function FirstEncounterPage() {
  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-cream to-sand/30">
      {/* Progress Bar */}
      <ProgressBar currentStep={2} totalSteps={3} steps={["Entretien découverte", "Informations", "Confirmation"]} />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Back Button */}
        <div className="mb-8">
          <Button asChild variant="ghost" className="text-olive hover:text-gold">
            <Link href="/reservation" className="flex items-center">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Retour au choix des services
            </Link>
          </Button>
        </div>

        {/* Step Indicator */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20">
            <span className="text-sm font-crimson text-gold font-medium">Étape 2/3 - Planification de l'appel</span>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Service Details */}
          <div>
            <Card className="bg-white/80 backdrop-blur-sm border-sand/30 mb-8">
              <CardHeader>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center">
                    <Phone className="w-8 h-8 text-gold" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="font-new-york text-2xl text-olive mb-2">Entretien découverte</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-olive/70">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        <span className="font-doulos">15-20 min</span>
                      </div>
                      <div className="flex items-center text-gold font-semibold">
                        <span className="font-doulos">Gratuit</span>
                      </div>
                    </div>
                  </div>
                </div>
                <p className="font-doulos text-olive/80 leading-relaxed">
                  Un premier échange téléphonique pour faire connaissance, comprendre vos besoins et définir ensemble
                  l'accompagnement qui vous convient le mieux.
                </p>
              </CardHeader>

              <CardContent>
                <h4 className="font-new-york text-lg font-semibold text-olive mb-4">Déroulement de l'entretien :</h4>
                <ul className="space-y-3">
                  {[
                    "Présentation mutuelle et mise en confiance",
                    "Écoute de votre situation et de vos attentes",
                    "Explication de mes méthodes d'accompagnement",
                    "Conseils personnalisés selon vos besoins",
                    "Définition du service le plus adapté",
                    "Réponses à toutes vos questions",
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-gold mt-0.5 mr-3 flex-shrink-0" />
                      <span className="font-doulos text-olive/80 text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-gradient-to-br from-gold/5 to-cream/50 border-sand/30">
              <CardContent className="p-6">
                <Heart className="w-8 h-8 text-gold mb-4" />
                <h4 className="font-new-york text-lg font-semibold text-olive mb-3">
                  Pourquoi commencer par cet entretien ?
                </h4>
                <div className="space-y-2 text-sm font-doulos text-olive/80">
                  <p>
                    • <strong>Sans engagement</strong> : vous décidez ensuite si vous souhaitez poursuivre
                  </p>
                  <p>
                    • <strong>Personnalisé</strong> : nous définissons ensemble votre parcours
                  </p>
                  <p>
                    • <strong>Rassurant</strong> : vous apprenez à me connaître avant de vous engager
                  </p>
                  <p>
                    • <strong>Efficace</strong> : gain de temps pour cibler vos vrais besoins
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Reservation Form */}
          <div>
            <FirstEncounterForm />
          </div>
        </div>
      </div>
    </div>
  )
}
