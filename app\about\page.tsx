import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Award, BookOpen, Calendar, Compass, Heart, Sparkles, Star, Target, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function About() {
	const timeline = [
		{
			year: "2012",
			title: "Découverte des soins énergétiques",
			description: "Première formation en Reiki et découverte de ma sensibilité aux énergies subtiles.",
		},
		{
			year: "2015",
			title: "Certification praticienne",
			description: "Obtention de ma certification en soins énergétiques et début de ma pratique professionnelle.",
		},
		{
			year: "2018",
			title: "Formation en coaching",
			description: "Spécialisation en coaching de vie et PNL pour enrichir mon accompagnement.",
		},
		{
			year: "2020",
			title: "Expertise en libération émotionnelle",
			description: "Formation avancée en techniques de libération des blocages émotionnels.",
		},
		{
			year: "2023",
			title: "Plus de 500 accompagnements",
			description: "Milestone important avec une clientèle fidèle et des résultats durables.",
		},
	];

	const certifications = [
		{
			title: "Praticienne Reiki Usui",
			organization: "Fédération Française de Reiki",
			year: "2015",
		},
		{
			title: "Coach certifiée PNL",
			organization: "Institut Français de PNL",
			year: "2018",
		},
		{
			title: "Spécialiste EFT",
			organization: "EFT France",
			year: "2020",
		},
		{
			title: "Harmonisation des Chakras",
			organization: "École Européenne d'Énergétique",
			year: "2021",
		},
	];

	const values = [
		{
			icon: <Heart className="w-6 h-6" />,
			title: "Bienveillance",
			description: "Chaque personne est accueillie avec respect et sans jugement, dans un espace sécurisé.",
		},
		{
			icon: <Target className="w-6 h-6" />,
			title: "Efficacité",
			description: "Des méthodes éprouvées et personnalisées pour des résultats concrets et durables.",
		},
		{
			icon: <Sparkles className="w-6 h-6" />,
			title: "Authenticité",
			description: "Une approche sincère qui honore votre unicité et respecte votre rythme.",
		},
		{
			icon: <Compass className="w-6 h-6" />,
			title: "Guidance",
			description: "Un accompagnement qui vous aide à trouver vos propres réponses et votre voie.",
		},
	];

	return (
		<div className="pt-20">
			{/* Hero Section */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid lg:grid-cols-2 gap-12 items-center">
						<div className="text-center lg:text-left">
							<div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20 mb-6">
								<Heart className="w-4 h-4 text-gold mr-2" />
								<span className="text-sm font-crimson text-gold font-medium">Mon histoire</span>
							</div>

							<h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
								Laurence Gémin
							</h1>

							<p className="font-crimson text-xl text-gold italic mb-6">
								"Accompagner chaque être vers sa lumière intérieure"
							</p>

							<p className="font-doulos text-lg text-olive/80 leading-relaxed mb-8">
								Passionnée par l'accompagnement humain depuis plus de 10 ans, j'ai fait de ma
								sensibilité aux énergies subtiles un véritable outil de transformation pour aider les
								autres à retrouver leur équilibre et révéler leur potentiel authentique.
							</p>

							<div className="flex flex-col sm:flex-row flex-wrap gap-4 justify-center lg:justify-start items-center">
								<Button
									asChild
									size="lg"
									className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
								>
									<Link href="/reservation" className="flex items-center">
										Prendre rendez-vous
										<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
									</Link>
								</Button>

								<Button
									asChild
									variant="outline"
									size="lg"
									className="border-2 border-gold text-gold bg-cream hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
								>
									<Link href="/services">Mes services</Link>
								</Button>
							</div>
						</div>

						<div className="relative">
							<div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-sand/20 to-gold/10">
								<Image
									src="/placeholder.svg?key=about-hero"
									alt="Laurence Gémin - Coach en soins énergétiques"
									width={600}
									height={600}
									className="object-cover w-full h-full"
								/>
							</div>
							<div className="absolute -bottom-6 -right-6 w-24 h-24 bg-gold/10 rounded-full flex items-center justify-center">
								<Sparkles className="w-8 h-8 text-gold animate-pulse" />
							</div>
							<div className="absolute -top-6 -left-6 w-16 h-16 bg-cream/80 rounded-full flex items-center justify-center">
								<Star className="w-6 h-6 text-gold" />
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Story Section */}
			<section className="py-20 bg-white/80">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-6">
							Mon chemin vers les soins énergétiques
						</h2>
						<p className="font-doulos text-lg text-olive/80 leading-relaxed">
							Découvrez comment ma propre quête de bien-être m'a menée à accompagner les autres dans leur
							transformation personnelle.
						</p>
					</div>

					<div className="prose prose-lg max-w-none">
						<div className="space-y-8">
							<Card className="bg-gradient-to-br from-cream/50 to-sand/20 border-sand/30">
								<CardContent className="p-8">
									<BookOpen className="w-8 h-8 text-gold mb-4" />
									<h3 className="font-new-york text-xl font-semibold text-olive mb-4">
										Les prémices d'une vocation
									</h3>
									<p className="font-doulos text-olive/80 leading-relaxed">
										Tout a commencé par une période difficile de ma vie où je cherchais des réponses
										au-delà de la médecine traditionnelle. C'est lors d'une séance de Reiki que j'ai
										découvert ma sensibilité particulière aux énergies subtiles et ressenti un appel
										profond à aider les autres à travers cette approche holistique.
									</p>
								</CardContent>
							</Card>

							<Card className="bg-gradient-to-br from-gold/5 to-cream/30 border-sand/30">
								<CardContent className="p-8">
									<Sparkles className="w-8 h-8 text-gold mb-4" />
									<h3 className="font-new-york text-xl font-semibold text-olive mb-4">
										Formation et développement
									</h3>
									<p className="font-doulos text-olive/80 leading-relaxed">
										J'ai alors entrepris un parcours de formation rigoureux, multipliant les
										apprentissages auprès de maîtres reconnus. Reiki, PNL, EFT, harmonisation des
										chakras... Chaque technique apprise enrichit ma palette d'outils pour mieux
										accompagner chaque personne selon ses besoins spécifiques.
									</p>
								</CardContent>
							</Card>

							<Card className="bg-gradient-to-br from-olive/5 to-sand/20 border-sand/30">
								<CardContent className="p-8">
									<Heart className="w-8 h-8 text-gold mb-4" />
									<h3 className="font-new-york text-xl font-semibold text-olive mb-4">
										Une mission de vie
									</h3>
									<p className="font-doulos text-olive/80 leading-relaxed">
										Aujourd'hui, après plus de 500 accompagnements, je sais que ma mission est
										d'aider chaque personne à retrouver sa lumière intérieure. Chaque séance est
										unique, chaque parcours est respecté, et chaque transformation me rappelle
										pourquoi j'ai choisi cette voie magnifique.
									</p>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</section>

			{/* Timeline Section */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-6">
							Mon parcours professionnel
						</h2>
						<p className="font-doulos text-lg text-olive/80">
							Les étapes clés qui ont façonné mon expertise
						</p>
					</div>

					<div className="relative">
						{/* Timeline Line */}
						<div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gold/30"></div>

						<div className="space-y-12">
							{timeline.map((item, index) => (
								<div key={index} className="relative flex items-start">
									<div className="flex-shrink-0 w-16 h-16 bg-gold rounded-full flex items-center justify-center text-white font-bold text-lg font-new-york relative z-10">
										{item.year.slice(-2)}
									</div>
									<div className="ml-8 flex-1">
										<Card className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-lg transition-shadow duration-300">
											<CardContent className="p-6">
												<div className="flex items-center justify-between mb-2">
													<h3 className="font-new-york text-lg font-semibold text-olive">
														{item.title}
													</h3>
													<span className="text-sm font-doulos text-gold font-medium">
														{item.year}
													</span>
												</div>
												<p className="font-doulos text-olive/80 leading-relaxed">
													{item.description}
												</p>
											</CardContent>
										</Card>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Certifications Section */}
			<section className="py-20 bg-white/80">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-6">
							Certifications & Formations
						</h2>
						<p className="font-doulos text-lg text-olive/80">
							Une expertise reconnue et en constante évolution
						</p>
					</div>

					<div className="grid md:grid-cols-2 gap-6">
						{certifications.map((cert, index) => (
							<Card
								key={index}
								className="bg-gradient-to-br from-cream/50 to-sand/20 border-sand/30 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
							>
								<CardContent className="p-6">
									<div className="flex items-start space-x-4">
										<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center flex-shrink-0">
											<Award className="w-6 h-6 text-gold" />
										</div>
										<div className="flex-1">
											<h3 className="font-new-york text-lg font-semibold text-olive mb-1">
												{cert.title}
											</h3>
											<p className="font-doulos text-olive/70 text-sm mb-2">
												{cert.organization}
											</p>
											<span className="inline-flex items-center px-2 py-1 rounded-full bg-gold/10 text-gold text-xs font-medium">
												{cert.year}
											</span>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Values Section */}
			<section className="py-20 bg-gradient-to-br from-olive/5 to-gold/5">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-6">
							Mes valeurs fondamentales
						</h2>
						<p className="font-doulos text-lg text-olive/80">
							Les principes qui guident chacun de mes accompagnements
						</p>
					</div>

					<div className="grid md:grid-cols-2 gap-8">
						{values.map((value, index) => (
							<Card
								key={index}
								className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
							>
								<CardContent className="p-8 text-center">
									<div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center">
										<div className="text-gold">{value.icon}</div>
									</div>
									<h3 className="font-new-york text-xl font-semibold text-olive mb-4">
										{value.title}
									</h3>
									<p className="font-doulos text-olive/80 leading-relaxed">{value.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Stats Section */}
			<section className="py-20 bg-white/80">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid md:grid-cols-3 gap-8 text-center">
						<div className="group">
							<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
								<Users className="w-10 h-10 text-gold" />
							</div>
							<div className="font-new-york text-3xl font-bold text-olive mb-2">500+</div>
							<p className="font-doulos text-olive/70">Accompagnements réalisés</p>
						</div>

						<div className="group">
							<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
								<Calendar className="w-10 h-10 text-gold" />
							</div>
							<div className="font-new-york text-3xl font-bold text-olive mb-2">10+</div>
							<p className="font-doulos text-olive/70">Années d'expérience</p>
						</div>

						<div className="group">
							<div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
								<Star className="w-10 h-10 text-gold" />
							</div>
							<div className="font-new-york text-3xl font-bold text-olive mb-2">98%</div>
							<p className="font-doulos text-olive/70">Clients satisfaits</p>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-gold/10 to-sand/20">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-sand/30">
						<Heart className="w-12 h-12 text-gold mx-auto mb-6" />

						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Prêt(e) à commencer votre transformation ?
						</h2>

						<p className="font-doulos text-lg text-olive/80 mb-8 max-w-2xl mx-auto">
							Chaque parcours est unique. Offrons-nous un premier échange pour découvrir comment je peux
							vous accompagner vers votre mieux-être.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-gold hover:bg-gold/90 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
							>
								<Link href="/reservation" className="flex items-center">
									Prendre rendez-vous
									<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-gold text-gold hover:bg-gold hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
							>
								<Link href="/testimonials">Lire les témoignages</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
