# 🌟 <PERSON> Website

Site web officiel de <PERSON> - Coach B<PERSON>-être

## 🚀 Status

✅ **Production Ready** - Le site est déployé et fonctionnel !

- **Local Development**: http://localhost:3000
- **Production**: https://laurence-gemin-website-o6k25utcf-benjamin-viranins-projects.vercel.app
- **Vercel Dashboard**: https://vercel.com/benjamin-viranins-projects/laurence-gemin-website

## 🛠️ Technologies

- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Animations**: Framer Motion
- **Deployment**: Vercel
- **Package Manager**: pnpm

## 🏃‍♂️ Quick Start

### Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Open http://localhost:3000
```

### Production

```bash
# Build for production
pnpm build

# Start production server
pnpm start

# Type checking
pnpm type-check
```

## 📁 Project Structure

```
laurence-gemin-website/
├── app/                    # Next.js App Router
│   ├── about/             # À propos page
│   ├── contact/           # Contact page
│   ├── events/            # Events page
│   ├── paiement/          # Payment pages
│   ├── reservation/       # Reservation pages
│   ├── services/          # Services page
│   ├── testimonials/      # Testimonials page
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── ui/               # UI components (Radix)
│   ├── ContactForm.tsx   # Contact form
│   ├── Header.tsx        # Navigation header
│   ├── Hero.tsx          # Hero section
│   └── ...
├── hooks/                # Custom React hooks
├── lib/                  # Utilities
├── public/               # Static assets
├── styles/               # Global styles
└── ...
```

## 🎨 Features

- ✅ Responsive design
- ✅ Modern UI with Tailwind CSS
- ✅ Smooth animations with Framer Motion
- ✅ Contact form with validation
- ✅ Service booking system
- ✅ Testimonials slider
- ✅ SEO optimized
- ✅ Performance optimized
- ✅ Accessibility compliant

## 🚀 Deployment

Le site est automatiquement déployé sur Vercel. Voir [DEPLOYMENT.md](./DEPLOYMENT.md) pour plus de détails.

### Automatic Deployments

- **Production**: Déploiement automatique depuis la branche `main`
- **Preview**: Déploiement automatique pour chaque PR

## 📊 Performance

- **Build Size**: ~101-141 kB par page
- **Static Pages**: 13 pages générées statiquement
- **Core Web Vitals**: Optimisé pour les métriques de performance

## 🔧 Configuration

### Environment Variables

Aucune variable d'environnement requise pour le moment.

### Next.js Configuration

Configuration optimisée dans `next.config.mjs`:
- Images optimisées (WebP, AVIF)
- Compression activée
- Headers de sécurité
- Cache optimisé

## 📞 Support

Pour toute question ou problème:
1. Vérifier les logs Vercel
2. Tester le build local
3. Consulter la documentation Next.js

---

**Développé avec ❤️ pour Laurence Gémin**
