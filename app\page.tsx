import Hero from "@/components/Hero";
import ServiceCard from "@/components/ServiceCard";
import TestimonialSlider from "@/components/TestimonialSlider";
import { Button } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Heart, Shield, Sparkles, Star, Users, Zap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function Home() {
	const services = [
		{
			title: "Soin énergétique complet",
			description: "Rééquilibrage global de vos énergies pour retrouver harmonie et vitalité",
			duration: "1h30",
			price: "85",
			features: [
				"Analyse énergétique approfondie",
				"Nettoyage et harmonisation",
				"Conseils personnalisés",
				"Suivi post-séance",
			],
			href: "/reservation/soin-energetique",
			icon: <Sparkles className="w-8 h-8" />,
		},
		{
			title: "Coaching bien-être",
			description: "Accompagnement personnalisé pour développer votre potentiel et votre épanouissement",
			duration: "1h",
			price: "65",
			features: [
				"Définition d'objectifs clairs",
				"Techniques de développement personnel",
				"Plan d'action personnalisé",
				"Suivi régulier",
			],
			href: "/reservation/coaching-bien-etre",
			icon: <Heart className="w-8 h-8" />,
		},
		{
			title: "Harmonisation chakras",
			description: "Équilibrage de vos centres énergétiques pour une circulation optimale",
			duration: "1h",
			price: "70",
			features: [
				"Diagnostic des 7 chakras principaux",
				"Techniques de rééquilibrage",
				"Méditation guidée",
				"Conseils d'entretien",
			],
			href: "/reservation/harmonisation-chakras",
			icon: <Zap className="w-8 h-8" />,
		},
	];

	const values = [
		{
			icon: <Heart className="w-8 h-8 text-gold" />,
			title: "Bienveillance",
			description: "Un accompagnement dans le respect et l'écoute de votre rythme",
		},
		{
			icon: <Shield className="w-8 h-8 text-gold" />,
			title: "Confidentialité",
			description: "Un espace sécurisé où vous pouvez vous exprimer en toute confiance",
		},
		{
			icon: <Sparkles className="w-8 h-8 text-gold" />,
			title: "Authenticité",
			description: "Une approche sincère pour révéler votre véritable essence",
		},
		{
			icon: <Users className="w-8 h-8 text-gold" />,
			title: "Personnalisation",
			description: "Chaque accompagnement est unique et adapté à vos besoins",
		},
	];

	return (
		<div className="overflow-hidden">
			{/* Hero Section */}
			<Hero />

			{/* Values Section */}
			<section className="py-24 bg-sand/30">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-20">
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-gold/15 border-2 border-gold/30 mb-8 shadow-lg">
							<Star className="w-5 h-5 text-gold mr-3" />
							<span className="text-lg font-dm-serif text-gold font-bold">Mes valeurs</span>
						</div>
						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-olive mb-6">
							Un accompagnement basé sur la confiance
						</h2>
						<p className="font-sans text-xl text-olive/90 max-w-2xl mx-auto font-medium leading-relaxed">
							Chaque séance est un moment privilégié où vous êtes accueilli(e) dans un espace de
							bienveillance et de respect absolu.
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-10">
						{values.map((value, index) => (
							<div
								key={index}
								className="text-center group hover:scale-105 transition-transform duration-300 bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl"
							>
								<div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-gold/20 to-gold/10 rounded-full flex items-center justify-center group-hover:shadow-lg transition-shadow duration-300">
									{value.icon}
								</div>
								<h3 className="font-dm-serif text-2xl font-bold text-olive mb-4">{value.title}</h3>
								<p className="font-sans text-olive/80 text-base leading-relaxed font-medium">
									{value.description}
								</p>
							</div>
						))}
					</div>
				</div>
			</section>

			{/* Services Section */}
			<section className="py-24 bg-gradient-to-br from-cream to-blue/5">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-20">
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-blue/15 border-2 border-blue/30 mb-8 shadow-lg">
							<Sparkles className="w-5 h-5 text-blue mr-3" />
							<span className="text-lg font-dm-serif text-blue font-bold">Mes services</span>
						</div>
						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-olive mb-6">
							Des soins adaptés à vos besoins
						</h2>
						<p className="font-sans text-xl text-olive/90 max-w-2xl mx-auto font-medium leading-relaxed">
							Découvrez mes différentes approches pour vous accompagner vers un mieux-être durable et
							authentique.
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-12">
						{services.map((service, index) => (
							<ServiceCard key={index} {...service} />
						))}
					</div>

					<div className="text-center">
						<Button
							asChild
							size="lg"
							className="bg-gold hover:bg-gold-dark text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg"
						>
							<Link href="/reservation" className="flex items-center">
								Voir tous mes services
								<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
							</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* About Preview */}
			<section className="py-24 bg-gradient-to-br from-sand/30 to-blue/10">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid lg:grid-cols-2 gap-16 items-center text-center lg:text-left">
						<div className="relative flex justify-center lg:block mb-8 lg:mb-0">
							<div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-blue/10 to-gold/10 inline-block shadow-2xl">
								<Image
									src="/placeholder.svg?key=ww41z"
									alt="Laurence Gémin"
									width={500}
									height={500}
									className="object-cover w-full h-full"
								/>
							</div>
							<div className="absolute -bottom-6 -right-6 w-28 h-28 bg-gold/15 rounded-full flex items-center justify-center shadow-lg">
								<Sparkles className="w-10 h-10 text-gold animate-pulse" />
							</div>
						</div>

						<div className="flex flex-col items-center lg:items-start">
							<div className="inline-flex items-center px-6 py-3 rounded-full bg-gold/15 border-2 border-gold/30 mb-8 shadow-lg">
								<Heart className="w-5 h-5 text-gold mr-3" />
								<span className="text-lg font-dm-serif text-gold font-bold">Mon parcours</span>
							</div>

							<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-olive mb-8">
								Une passion devenue mission
							</h2>

							<div className="space-y-6 mb-10">
								<p className="font-sans text-olive/90 leading-relaxed text-lg font-medium">
									Depuis plus de 10 ans, j'accompagne les personnes dans leur quête d'équilibre et de
									bien-être. Mon approche holistique combine techniques énergétiques traditionnelles
									et méthodes de coaching modernes.
								</p>

								<p className="font-sans text-olive/90 leading-relaxed text-lg font-medium">
									Certifiée en soins énergétiques et formée aux techniques de libération émotionnelle,
									je vous guide avec bienveillance vers votre épanouissement personnel.
								</p>
							</div>

							<div className="space-y-4 mb-10 w-full flex flex-col items-center lg:items-start">
								{[
									"Praticienne certifiée en soins énergétiques",
									"Formation en coaching de vie et PNL",
									"Spécialisée en libération émotionnelle",
									"Plus de 500 accompagnements réalisés",
								].map((item, index) => (
									<div
										key={index}
										className="flex items-center bg-white/60 px-4 py-3 rounded-lg shadow-sm"
									>
										<CheckCircle className="w-6 h-6 text-gold mr-4 flex-shrink-0" />
										<span className="font-sans text-olive/90 font-medium text-base">{item}</span>
									</div>
								))}
							</div>

							<Button
								asChild
								size="lg"
								variant="outline"
								className="border-2 border-blue text-blue hover:bg-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
							>
								<Link href="/about">Découvrir mon histoire</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* Testimonials Section */}
			<section className="py-24 bg-gradient-to-br from-blue/5 to-gold/5">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-20">
						<div className="inline-flex items-center px-6 py-3 rounded-full bg-blue/15 border-2 border-blue/30 mb-8 shadow-lg">
							<Users className="w-5 h-5 text-blue mr-3" />
							<span className="text-lg font-dm-serif text-blue font-bold">Témoignages</span>
						</div>
						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-olive mb-6">
							Ils ont retrouvé leur équilibre
						</h2>
						<p className="font-sans text-xl text-olive/90 max-w-2xl mx-auto font-medium leading-relaxed">
							Découvrez les témoignages de personnes qui ont fait confiance à mon accompagnement pour
							transformer leur vie.
						</p>
					</div>

					<TestimonialSlider />

					<div className="text-center mt-12">
						<Button
							asChild
							variant="outline"
							size="lg"
							className="border-2 border-blue text-blue hover:bg-blue hover:text-white font-sans font-bold px-10 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
						>
							<Link href="/testimonials">Voir tous les témoignages</Link>
						</Button>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-24 bg-gradient-to-br from-gold/15 to-blue/10">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="bg-white/90 backdrop-blur-sm rounded-3xl p-10 md:p-16 shadow-2xl border-2 border-gold/20">
						<Sparkles className="w-16 h-16 text-gold mx-auto mb-8" />

						<h2 className="font-early-sunday text-4xl md:text-5xl font-bold text-olive mb-6">
							Prêt(e) à commencer votre transformation ?
						</h2>

						<p className="font-sans text-xl text-olive/90 mb-10 max-w-2xl mx-auto font-medium leading-relaxed">
							Offrez-vous un moment privilégié pour reconnecter avec votre essence et révéler votre
							potentiel authentique.
						</p>

						<div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-gold hover:bg-gold-dark text-white font-sans font-bold px-12 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group text-lg"
							>
								<Link href="/reservation" className="flex items-center">
									Prendre rendez-vous
									<ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-blue text-blue bg-cream hover:bg-blue hover:text-white font-sans font-bold px-12 py-4 rounded-full transition-all duration-300 hover:shadow-lg text-lg"
							>
								<Link href="/reservation">Découvrir les services</Link>
							</Button>
						</div>

						<p className="font-sans text-base text-olive/70 mt-8 font-medium">
							Premier échange téléphonique gratuit • Paiement sécurisé • Satisfaction garantie
						</p>
					</div>
				</div>
			</section>
		</div>
	);
}
