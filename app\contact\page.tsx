import ContactForm from "@/components/ContactForm";
import FAQSection from "@/components/FAQSection";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Calendar, Clock, Heart, MapPin, MessageCircle, Phone } from "lucide-react";
import Link from "next/link";

export default function Contact() {
	return (
		<div className="pt-20">
			{/* Hero Section */}
			<section className="py-20 bg-gradient-to-br from-blue/10 to-gold/10">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<div className="inline-flex items-center px-6 py-3 rounded-full bg-gold/15 border-2 border-gold/30 mb-8 shadow-lg">
						<MessageCircle className="w-5 h-5 text-gold mr-3" />
						<span className="text-lg font-dm-serif text-gold font-bold">Contact</span>
					</div>

					<h1 className="font-new-york text-4xl md:text-5xl font-bold text-olive mb-6">
						Prenons rendez-vous
					</h1>

					<p className="font-doulos text-lg text-olive/80 leading-relaxed mb-8 max-w-2xl mx-auto">
						Je serais ravie d'échanger avec vous sur vos besoins et de vous accompagner dans votre parcours
						vers le mieux-être. Contactez-moi pour planifier votre première séance ou simplement pour poser
						vos questions.
					</p>

					<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-sand/30 inline-block">
						<div className="flex items-center justify-center space-x-6 text-sm font-doulos text-olive/80">
							<div className="flex items-center">
								<Phone className="w-4 h-4 text-gold mr-2" />
								<span>Premier échange gratuit</span>
							</div>
							<div className="flex items-center">
								<Calendar className="w-4 h-4 text-gold mr-2" />
								<span>Réponse sous 24h</span>
							</div>
							<div className="flex items-center">
								<Heart className="w-4 h-4 text-gold mr-2" />
								<span>Accompagnement bienveillant</span>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Contact Form Section */}
			<section className="py-20 bg-sand/20">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<ContactForm />
				</div>
			</section>

			{/* Additional Info */}
			<section className="py-20 bg-gradient-to-br from-blue/5 to-gold/5">
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Informations pratiques
						</h2>
						<p className="font-doulos text-lg text-olive/80">
							Tout ce que vous devez savoir avant notre première rencontre
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						<Card className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-lg transition-all duration-300">
							<CardHeader>
								<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center mb-4">
									<Clock className="w-6 h-6 text-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-olive">Horaires</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2 font-doulos text-olive/80 text-sm">
									<div className="flex justify-between">
										<span>Lundi - Vendredi</span>
										<span>9h00 - 19h00</span>
									</div>
									<div className="flex justify-between">
										<span>Samedi</span>
										<span>9h00 - 17h00</span>
									</div>
									<div className="flex justify-between">
										<span>Dimanche</span>
										<span>Fermé</span>
									</div>
									<div className="pt-2 border-t border-sand/30">
										<p className="text-xs text-olive/60">
											Horaires flexibles selon vos contraintes
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-lg transition-all duration-300">
							<CardHeader>
								<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center mb-4">
									<MapPin className="w-6 h-6 text-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-olive">Modalités</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-3 font-doulos text-olive/80 text-sm">
									<div>
										<h4 className="font-semibold text-olive mb-1">En cabinet</h4>
										<p>Séances en présentiel dans un cadre chaleureux et apaisant</p>
									</div>
									<div>
										<h4 className="font-semibold text-olive mb-1">À distance</h4>
										<p>Soins énergétiques par téléphone ou visioconférence</p>
									</div>
									<div>
										<h4 className="font-semibold text-olive mb-1">À domicile</h4>
										<p>Possible selon la localisation (supplément appliqué)</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="bg-white/80 backdrop-blur-sm border-sand/30 hover:shadow-lg transition-all duration-300">
							<CardHeader>
								<div className="w-12 h-12 bg-gold/10 rounded-full flex items-center justify-center mb-4">
									<MessageCircle className="w-6 h-6 text-gold" />
								</div>
								<CardTitle className="font-new-york text-lg text-olive">Premier contact</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-3 font-doulos text-olive/80 text-sm">
									<div>
										<h4 className="font-semibold text-olive mb-1">Échange gratuit</h4>
										<p>15-20 minutes pour faire connaissance et répondre à vos questions</p>
									</div>
									<div>
										<h4 className="font-semibold text-olive mb-1">Sans engagement</h4>
										<p>Vous décidez ensuite si vous souhaitez poursuivre</p>
									</div>
									<div>
										<h4 className="font-semibold text-olive mb-1">Personnalisé</h4>
										<p>Nous définissons ensemble l'approche qui vous convient</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className="py-20 bg-sand/20">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="font-new-york text-3xl md:text-4xl font-bold text-olive mb-4">
							Questions fréquentes
						</h2>
						<p className="font-doulos text-lg text-olive/80">
							Les réponses aux questions que vous vous posez avant de prendre rendez-vous
						</p>
					</div>

					<FAQSection
						faqs={[
							{
								question: "Comment se déroule le premier rendez-vous ?",
								answer: "Nous commençons toujours par un échange téléphonique gratuit de 15-20 minutes pour faire connaissance, comprendre vos besoins et répondre à vos questions. Si vous souhaitez poursuivre, nous planifions ensuite votre première séance selon vos disponibilités.",
							},
							{
								question: "Quels sont vos tarifs ?",
								answer: "Mes tarifs varient selon le type de séance : 60€ pour un soin à distance, 65€ pour un coaching bien-être, 70-85€ pour les soins énergétiques complets. Le premier échange téléphonique est toujours gratuit. Des forfaits sont disponibles pour un suivi régulier.",
							},
							{
								question: "Acceptez-vous les paiements échelonnés ?",
								answer: "Oui, je comprends que l'investissement dans son bien-être puisse représenter un budget. Nous pouvons discuter ensemble de modalités de paiement adaptées à votre situation. L'important est que vous puissiez accéder aux soins dont vous avez besoin.",
							},
							{
								question: "Combien de temps dure une séance ?",
								answer: "La durée varie selon le type de soin : 1h pour un coaching ou un soin à distance, 1h15 pour une libération émotionnelle, 1h30 pour un soin énergétique complet. Je prends toujours le temps nécessaire, sans vous presser.",
							},
							{
								question: "Les soins à distance sont-ils vraiment efficaces ?",
								answer: "Absolument ! L'énergie n'a pas de frontières physiques. Mes clients à distance obtiennent les mêmes résultats qu'en présentiel. La connexion énergétique se fait naturellement, et vous bénéficiez du confort de votre domicile.",
							},
							{
								question: "Puis-je annuler ou reporter un rendez-vous ?",
								answer: "Bien sûr, je comprends que les imprévus arrivent. Il suffit de me prévenir au moins 24h à l'avance pour reporter sans frais. En cas d'urgence, nous trouvons toujours une solution ensemble.",
							},
						]}
						description="Les réponses aux questions que vous vous posez avant de prendre rendez-vous"
					/>
				</div>
			</section>

			{/* Testimonial Section */}
			<section className="py-20 bg-gradient-to-br from-cream to-sand/30">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
					<Card className="bg-white/80 backdrop-blur-sm border-sand/30 shadow-xl">
						<CardContent className="p-8 md:p-12 text-center">
							<div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-gold/10 to-gold/5 rounded-full flex items-center justify-center">
								<Heart className="w-8 h-8 text-gold" />
							</div>

							<blockquote className="font-crimson text-xl text-olive italic leading-relaxed mb-6">
								"Laurence a cette capacité rare d'écouter avec son cœur et de comprendre exactement ce
								dont vous avez besoin. Dès notre premier échange, j'ai su que j'étais entre de bonnes
								mains. Son accompagnement a transformé ma vie."
							</blockquote>

							<div className="flex justify-center mb-4">
								{[...Array(5)].map((_, i) => (
									<div key={i} className="w-5 h-5 text-gold fill-current">
										⭐
									</div>
								))}
							</div>

							<cite className="font-doulos text-olive/70">
								— Marie-Claire D., accompagnée depuis 2 ans
							</cite>
						</CardContent>
					</Card>
				</div>
			</section>
			<section className="py-10">
				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<Link
						href="/reservation"
						className="flex items-center justify-center bg-gold text-white py-3 px-6 rounded-full font-doulos font-semibold hover:bg-olive transition-colors duration-300 group"
					>
						Prendre rendez-vous
						<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
					</Link>
				</div>
			</section>
		</div>
	);
}
