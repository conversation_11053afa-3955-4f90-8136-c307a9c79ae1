"use client";

import type React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle, Clock, Mail, MapPin, Phone, Send } from "lucide-react";
import { useState } from "react";

const ContactForm = () => {
	const [formData, setFormData] = useState({
		firstName: "",
		email: "",
		phone: "",
		service: "",
		timeSlot: "",
		message: "",
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmitted, setIsSubmitted] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);

		// Simulate form submission
		await new Promise((resolve) => setTimeout(resolve, 2000));

		setIsSubmitting(false);
		setIsSubmitted(true);
	};

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	if (isSubmitted) {
		return (
			<Card className="max-w-2xl mx-auto bg-white/80 backdrop-blur-sm border-sand/50">
				<CardContent className="text-center py-12">
					<CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
					<h3 className="font-new-york text-2xl font-bold text-olive mb-4">Merci pour votre message !</h3>
					<p className="font-doulos text-olive/80 mb-6">
						J'ai bien reçu votre demande et je vous recontacterai dans les plus brefs délais pour convenir
						d'un rendez-vous qui vous convient.
					</p>
					<Button
						onClick={() => setIsSubmitted(false)}
						variant="outline"
						className="border-gold text-gold hover:bg-gold hover:text-white"
					>
						Envoyer un autre message
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
			{/* Contact Form */}
			<Card className="bg-white/80 backdrop-blur-sm border-sand/50">
				<CardHeader>
					<CardTitle className="font-new-york text-2xl text-olive text-center">Prendre rendez-vous</CardTitle>
					<p className="font-doulos text-olive/70 text-center">
						Remplissez ce formulaire et je vous recontacterai rapidement
					</p>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit} className="space-y-6">
						{/* Personal Info */}
						<div className="grid md:grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="firstName" className="font-doulos text-olive">
									Prénom *
								</Label>
								<Input
									id="firstName"
									value={formData.firstName}
									onChange={(e) => handleInputChange("firstName", e.target.value)}
									required
									className="border-sand focus:border-gold"
									placeholder="Votre prénom"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="email" className="font-doulos text-olive">
									Email *
								</Label>
								<Input
									id="email"
									type="email"
									value={formData.email}
									onChange={(e) => handleInputChange("email", e.target.value)}
									required
									className="border-sand focus:border-gold"
									placeholder="<EMAIL>"
								/>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="phone" className="font-doulos text-olive">
								Téléphone
							</Label>
							<Input
								id="phone"
								type="tel"
								value={formData.phone}
								onChange={(e) => handleInputChange("phone", e.target.value)}
								className="border-sand focus:border-gold"
								placeholder="06 12 34 56 78"
							/>
						</div>

						{/* Service Selection */}
						<div className="space-y-2">
							<Label className="font-doulos text-olive">Quel est votre besoin ? *</Label>
							<Select onValueChange={(value) => handleInputChange("service", value)}>
								<SelectTrigger className="border-sand focus:border-gold">
									<SelectValue placeholder="Choisissez un service" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="soin-energetique">Soin énergétique complet</SelectItem>
									<SelectItem value="coaching-bien-etre">Coaching bien-être</SelectItem>
									<SelectItem value="harmonisation-chakras">Harmonisation des chakras</SelectItem>
									<SelectItem value="liberation-emotionnelle">Libération émotionnelle</SelectItem>
									<SelectItem value="soin-distance">Soin à distance</SelectItem>
									<SelectItem value="autre">Autre / Je ne sais pas</SelectItem>
								</SelectContent>
							</Select>
						</div>

						{/* Time Slot */}
						<div className="space-y-2">
							<Label className="font-doulos text-olive">Créneaux préférés</Label>
							<Select onValueChange={(value) => handleInputChange("timeSlot", value)}>
								<SelectTrigger className="border-sand focus:border-gold">
									<SelectValue placeholder="Choisissez un créneau" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="matin">Matin (9h-12h)</SelectItem>
									<SelectItem value="apres-midi">Après-midi (14h-17h)</SelectItem>
									<SelectItem value="soir">Fin de journée (17h-19h)</SelectItem>
									<SelectItem value="weekend">Weekend</SelectItem>
									<SelectItem value="flexible">Je suis flexible</SelectItem>
								</SelectContent>
							</Select>
						</div>

						{/* Message */}
						<div className="space-y-2">
							<Label htmlFor="message" className="font-doulos text-olive">
								Votre message
							</Label>
							<Textarea
								id="message"
								value={formData.message}
								onChange={(e) => handleInputChange("message", e.target.value)}
								className="border-sand focus:border-gold min-h-[120px]"
								placeholder="Parlez-moi de votre situation, vos attentes, ou toute question que vous pourriez avoir..."
							/>
						</div>

						{/* Submit Button */}
						<Button
							type="submit"
							disabled={isSubmitting}
							className="w-full bg-gold hover:bg-gold/90 text-white font-doulos font-medium py-4 rounded-full transition-all duration-300 hover:shadow-lg disabled:opacity-50 min-h-[48px]"
						>
							{isSubmitting ? (
								<div className="flex items-center justify-center">
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
									Envoi en cours...
								</div>
							) : (
								<div className="flex items-center justify-center">
									<Send className="w-4 h-4 mr-2" />
									Envoyer ma demande
								</div>
							)}
						</Button>
					</form>
				</CardContent>
			</Card>

			{/* Contact Info */}
			<div className="space-y-6">
				<Card className="bg-white/80 backdrop-blur-sm border-sand/50">
					<CardHeader>
						<CardTitle className="font-new-york text-xl text-olive">Informations de contact</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center space-x-3">
							<div className="w-10 h-10 bg-gold/10 rounded-full flex items-center justify-center">
								<Phone className="w-5 h-5 text-gold" />
							</div>
							<div>
								<p className="font-doulos text-olive font-medium">Téléphone</p>
								<p className="font-doulos text-olive/70">06 12 34 56 78</p>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<div className="w-10 h-10 bg-gold/10 rounded-full flex items-center justify-center">
								<Mail className="w-5 h-5 text-gold" />
							</div>
							<div>
								<p className="font-doulos text-olive font-medium">Email</p>
								<p className="font-doulos text-olive/70"><EMAIL></p>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<div className="w-10 h-10 bg-gold/10 rounded-full flex items-center justify-center">
								<MapPin className="w-5 h-5 text-gold" />
							</div>
							<div>
								<p className="font-doulos text-olive font-medium">Localisation</p>
								<p className="font-doulos text-olive/70">Consultations en cabinet et à distance</p>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<div className="w-10 h-10 bg-gold/10 rounded-full flex items-center justify-center">
								<Clock className="w-5 h-5 text-gold" />
							</div>
							<div>
								<p className="font-doulos text-olive font-medium">Horaires</p>
								<p className="font-doulos text-olive/70">
									Lun-Ven: 9h-19h
									<br />
									Sam: 9h-17h
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Map Placeholder */}
				<Card className="bg-white/80 backdrop-blur-sm border-sand/50">
					<CardContent className="p-0">
						<div className="h-64 bg-gradient-to-br from-sand/20 to-gold/10 rounded-lg flex items-center justify-center">
							<div className="text-center">
								<MapPin className="w-12 h-12 text-gold/50 mx-auto mb-2" />
								<p className="font-doulos text-olive/60">Carte interactive</p>
								<p className="font-doulos text-olive/40 text-sm">
									Localisation précise communiquée lors de la prise de RDV
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default ContactForm;
