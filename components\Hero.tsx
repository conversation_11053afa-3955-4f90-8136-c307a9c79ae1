"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Spark<PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const Hero = () => {
	const [scrollY, setScrollY] = useState(0);

	useEffect(() => {
		const handleScroll = () => setScrollY(window.scrollY);
		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	return (
		<section className="relative min-h-screen flex items-end justify-center overflow-hidden mt-20">
			{/* Floating Elements with Parallax */}
			<div
				className="absolute top-20 left-10 animate-float"
				style={{ transform: `translateY(${scrollY * 0.2}px)` }}
			>
				<Sparkles className="w-8 h-8 text-gold/30" />
			</div>
			<div
				className="absolute bottom-32 right-16 animate-float"
				style={{
					animationDelay: "1s",
					transform: `translateY(${scrollY * -0.1}px)`,
				}}
			>
				<Sparkles className="w-6 h-6 text-gold/40" />
			</div>
			<div
				className="absolute top-1/3 right-20 animate-float"
				style={{
					animationDelay: "2s",
					transform: `translateY(${scrollY * 0.15}px)`,
				}}
			>
				<Sparkles className="w-4 h-4 text-gold/20" />
			</div>

			{/* Content */}
			<div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Desktop Grid Layout */}
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-[80vh]">
					{/* Text Content */}
					<div className="animate-fade-in text-center order-1 lg:order-1">
						{/* Badge */}
						<div className="inline-flex items-center px-4 py-2 rounded-full bg-gold/10 border border-gold/20 mb-8">
							<Sparkles className="w-4 h-4 text-gold mr-2" />
							<span className="text-sm font-crimson text-gold font-medium">
								Coach certifiée en soins énergétiques
							</span>
						</div>

						{/* Main Heading */}
						<h1 className="font-new-york text-4xl sm:text-5xl lg:text-6xl font-bold text-olive mb-6 leading-tight">
							Retrouvez votre
							<span className="block text-gold font-crimson italic">équilibre intérieur</span>
						</h1>

						{/* Subtitle */}
						<p className="font-doulos text-lg sm:text-xl text-olive/80 mb-8 max-w-2xl mx-auto leading-relaxed">
							Accompagnement personnalisé en soins énergétiques et coaching bien-être pour révéler votre
							potentiel et harmoniser votre être dans sa globalité.
						</p>

						{/* Quote */}
						<blockquote className="font-crimson italic text-xl text-gold mb-10 max-w-xl mx-auto">
							"Chaque être porte en lui une lumière unique qui ne demande qu'à rayonner"
						</blockquote>

						{/* CTA Buttons */}
						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								asChild
								size="lg"
								className="bg-gold hover:bg-gold/95 text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-xl hover:scale-105 group"
							>
								<Link href="/services" className="flex items-center">
									Découvrir mes services
									<ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
								</Link>
							</Button>

							<Button
								asChild
								variant="outline"
								size="lg"
								className="border-2 border-gold text-gold bg-cream hover:bg-gold/95 hover:text-white font-doulos font-medium px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg"
							>
								<Link href="/about">Mon parcours</Link>
							</Button>
						</div>

						{/* Trust Indicators */}
						<div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-olive/60">
							<div className="flex items-center">
								<div className="w-2 h-2 bg-gold rounded-full mr-2"></div>
								<span className="font-doulos">Certifiée praticienne énergétique</span>
							</div>
							<div className="flex items-center">
								<div className="w-2 h-2 bg-gold rounded-full mr-2"></div>
								<span className="font-doulos">Plus de 500 accompagnements</span>
							</div>
							<div className="flex items-center">
								<div className="w-2 h-2 bg-gold rounded-full mr-2"></div>
								<span className="font-doulos">Approche holistique personnalisée</span>
							</div>
						</div>
					</div>

					{/* Portrait Image */}
					<div className="animate-fade-in order-2 lg:order-2 flex justify-center lg:justify-end items-end h-full">
						<div
							className="relative w-full max-w-xs sm:max-w-sm lg:max-w-lg xl:max-w-xl ml-20 sm:ml-20 lg:ml-0"
							style={{ transform: `translateY(${scrollY * 0.05}px)` }}
						>
							<Image
								src="/laurence-gemin-portrait.png"
								alt="Laurence Gémin - Coach certifiée en soins énergétiques"
								width={500}
								height={600}
								className="w-full h-auto object-contain object-bottom drop-shadow-2xl"
								priority
							/>
							{/* Subtle glow effect behind the image */}
							<div className="absolute inset-0 bg-gradient-to-br from-gold/20 via-transparent to-sand/20 rounded-full blur-3xl -z-10 scale-110"></div>
						</div>
					</div>
				</div>
			</div>

			{/* Scroll Indicator */}
			<div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce hidden sm:block">
				<div className="w-6 h-10 border-2 border-gold/30 rounded-full flex justify-center">
					<div className="w-1 h-3 bg-gold/50 rounded-full mt-2 animate-pulse"></div>
				</div>
			</div>
		</section>
	);
};

export default Hero;
